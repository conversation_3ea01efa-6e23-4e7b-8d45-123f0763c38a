/**
 * Interviewer Service
 * 
 * Handles interviewer-specific operations including:
 * - Assessment management
 * - Candidate evaluations
 * - Earnings tracking
 * - Performance metrics
 * - Profile management
 * 
 * Note: Interview scheduling and video calls have been removed
 */

import { supabase } from '@/utils/supabaseClient';
import showToast from '@/utils/toast';

/**
 * Get interviewer profile data
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Profile data
 */
export const getInterviewerProfile = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('interviewer_profiles')
      .select('*')
      .eq('id', interviewerId)
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error fetching interviewer profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Update interviewer profile
 * @param {string} interviewerId - Interviewer ID
 * @param {Object} profileData - Profile data to update
 * @returns {Promise<Object>} - Update result
 */
export const updateInterviewerProfile = async (interviewerId, profileData) => {
  try {
    const { data, error } = await supabase
      .from('interviewer_profiles')
      .update({
        ...profileData,
        updated_at: new Date().toISOString()
      })
      .eq('id', interviewerId)
      .select()
      .single();

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error('Error updating interviewer profile:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get assessment requests for interviewer
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Assessment requests
 */
export const getAssessmentRequests = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('assessment_requests')
      .select(`
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          mobile_number,
          years_experience,
          current_job_title,
          current_company,
          profile_photo_url,
          skills
        ),
        jobs:job_id (
          id,
          title,
          description,
          requirements,
          companies:company_id (
            company_name,
            logo_url
          )
        )
      `)
      .eq('interviewer_id', interviewerId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error fetching assessment requests:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get completed assessments for interviewer
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Completed assessments
 */
export const getCompletedAssessments = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('assessment_requests')
      .select(`
        *,
        candidates:candidate_id (
          id,
          full_name,
          email,
          mobile_number,
          years_experience,
          current_job_title,
          current_company,
          profile_photo_url
        ),
        jobs:job_id (
          id,
          title,
          companies:company_id (
            company_name
          )
        ),
        evaluations:candidate_evaluations!assessment_id (
          id,
          overall_score,
          technical_score,
          communication_score,
          cultural_fit_score,
          problem_solving_score,
          experience_score,
          feedback,
          recommendation,
          strengths,
          areas_for_improvement,
          created_at
        )
      `)
      .eq('interviewer_id', interviewerId)
      .eq('status', 'completed')
      .order('updated_at', { ascending: false });

    if (error) throw error;
    return { success: true, data: data || [] };
  } catch (error) {
    console.error('Error fetching completed assessments:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Submit candidate evaluation
 * @param {string} assessmentId - Assessment ID
 * @param {Object} evaluationData - Evaluation data
 * @returns {Promise<Object>} - Submission result
 */
export const submitCandidateEvaluation = async (assessmentId, evaluationData) => {
  try {
    // Start transaction
    const { data: evaluation, error: evalError } = await supabase
      .from('candidate_evaluations')
      .insert({
        assessment_id: assessmentId,
        overall_score: evaluationData.overall_score,
        technical_score: evaluationData.technical_score,
        communication_score: evaluationData.communication_score,
        cultural_fit_score: evaluationData.cultural_fit_score,
        problem_solving_score: evaluationData.problem_solving_score,
        experience_score: evaluationData.experience_score,
        feedback: evaluationData.feedback,
        recommendation: evaluationData.recommendation,
        strengths: evaluationData.strengths,
        areas_for_improvement: evaluationData.areas_for_improvement,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (evalError) throw evalError;

    // Update assessment status
    const { error: updateError } = await supabase
      .from('assessment_requests')
      .update({ 
        status: 'completed',
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', assessmentId);

    if (updateError) throw updateError;

    return { success: true, data: evaluation };
  } catch (error) {
    console.error('Error submitting candidate evaluation:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interviewer earnings
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Earnings data
 */
export const getInterviewerEarnings = async (interviewerId) => {
  try {
    const { data, error } = await supabase
      .from('interviewer_earnings')
      .select(`
        *,
        assessment_requests:assessment_id (
          id,
          candidates:candidate_id (
            full_name
          ),
          jobs:job_id (
            title,
            companies:company_id (
              company_name
            )
          )
        )
      `)
      .eq('interviewer_id', interviewerId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    const totalEarnings = data?.reduce((sum, earning) => sum + earning.amount, 0) || 0;
    const thisMonthEarnings = data?.filter(earning => {
      const earningDate = new Date(earning.created_at);
      const now = new Date();
      return earningDate.getMonth() === now.getMonth() && 
             earningDate.getFullYear() === now.getFullYear();
    }).reduce((sum, earning) => sum + earning.amount, 0) || 0;

    return { 
      success: true, 
      data: {
        total: totalEarnings,
        thisMonth: thisMonthEarnings,
        history: data || []
      }
    };
  } catch (error) {
    console.error('Error fetching interviewer earnings:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get interviewer performance metrics
 * @param {string} interviewerId - Interviewer ID
 * @returns {Promise<Object>} - Performance metrics
 */
export const getPerformanceMetrics = async (interviewerId) => {
  try {
    // Get assessment statistics
    const { data: assessments, error: assessmentError } = await supabase
      .from('assessment_requests')
      .select(`
        *,
        evaluations:candidate_evaluations!assessment_id (
          overall_score,
          technical_score,
          communication_score,
          cultural_fit_score
        )
      `)
      .eq('interviewer_id', interviewerId);

    if (assessmentError) throw assessmentError;

    const totalAssessments = assessments?.length || 0;
    const completedAssessments = assessments?.filter(a => a.status === 'completed').length || 0;
    const pendingAssessments = assessments?.filter(a => a.status === 'pending').length || 0;

    // Calculate average scores
    const completedWithEvaluations = assessments?.filter(a => 
      a.status === 'completed' && a.evaluations && a.evaluations.length > 0
    ) || [];

    let averageOverallScore = 0;
    let averageTechnicalScore = 0;
    let averageCommunicationScore = 0;
    let averageCulturalFitScore = 0;

    if (completedWithEvaluations.length > 0) {
      const scores = completedWithEvaluations.map(a => a.evaluations[0]);
      
      averageOverallScore = scores.reduce((sum, s) => sum + (s.overall_score || 0), 0) / scores.length;
      averageTechnicalScore = scores.reduce((sum, s) => sum + (s.technical_score || 0), 0) / scores.length;
      averageCommunicationScore = scores.reduce((sum, s) => sum + (s.communication_score || 0), 0) / scores.length;
      averageCulturalFitScore = scores.reduce((sum, s) => sum + (s.cultural_fit_score || 0), 0) / scores.length;
    }

    const metrics = {
      totalAssessments,
      completedAssessments,
      pendingAssessments,
      completionRate: totalAssessments > 0 ? (completedAssessments / totalAssessments) * 100 : 0,
      averageScores: {
        overall: Math.round(averageOverallScore * 10) / 10,
        technical: Math.round(averageTechnicalScore * 10) / 10,
        communication: Math.round(averageCommunicationScore * 10) / 10,
        culturalFit: Math.round(averageCulturalFitScore * 10) / 10
      }
    };

    return { success: true, data: metrics };
  } catch (error) {
    console.error('Error fetching performance metrics:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Upload profile photo
 * @param {string} interviewerId - Interviewer ID
 * @param {File} file - Photo file
 * @returns {Promise<Object>} - Upload result
 */
export const uploadProfilePhoto = async (interviewerId, file) => {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${interviewerId}-${Date.now()}.${fileExt}`;
    const filePath = `interviewer-photos/${fileName}`;

    // Upload file to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('profile-photos')
      .upload(filePath, file);

    if (uploadError) throw uploadError;

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('profile-photos')
      .getPublicUrl(filePath);

    // Update profile with photo URL
    const { data: profileData, error: profileError } = await supabase
      .from('interviewer_profiles')
      .update({ 
        profile_photo_url: urlData.publicUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', interviewerId)
      .select()
      .single();

    if (profileError) throw profileError;

    return { success: true, data: { url: urlData.publicUrl, profile: profileData } };
  } catch (error) {
    console.error('Error uploading profile photo:', error);
    return { success: false, error: error.message };
  }
};

export default {
  getInterviewerProfile,
  updateInterviewerProfile,
  getAssessmentRequests,
  getCompletedAssessments,
  submitCandidateEvaluation,
  getInterviewerEarnings,
  getPerformanceMetrics,
  uploadProfilePhoto
};
