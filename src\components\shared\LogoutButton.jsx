import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Modal } from 'antd';
import { LogoutOutlined } from '@ant-design/icons';
import useAuth from '@/hooks/useAuth';
import showToast from '@/utils/toast';

/**
 * A reusable logout button component that can be used throughout the app
 * Includes confirmation modal and loading state
 */
const LogoutButton = ({
  type = 'primary',
  size = 'middle',
  danger = false,
  block = false,
  icon = <LogoutOutlined />,
  text = 'Logout',
  redirectTo = '/login',
  className = '',
  showConfirmation = true,
  forceReload = false,
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { logout, loading } = useAuth();
  const navigate = useNavigate();

  const showLogoutConfirmation = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const handleLogout = async () => {
    try {
      const { success, message } = await logout();

      if (success) {
        showToast.success('Logged out successfully');
        if (forceReload) {
          window.location.href = redirectTo;
        } else {
          navigate(redirectTo, { replace: true });
        }
      } else {
        throw new Error(message || 'Logout failed');
      }
    } catch (error) {
      console.error('Logout error:', error);
      showToast.error('Failed to logout: ' + (error.message || 'Unknown error'));
      // Still redirect on error to prevent stuck state
      if (forceReload) {
        window.location.href = redirectTo;
      } else {
        navigate(redirectTo, { replace: true });
      }
    }
  };

  const onLogoutConfirm = async () => {
    setIsModalVisible(false);
    await handleLogout();
  };

  const onButtonClick = () => {
    if (showConfirmation) {
      showLogoutConfirmation();
    } else {
      handleLogout();
    }
  };

  return (
    <>
      <Button
        type={type}
        danger={danger}
        icon={icon}
        size={size}
        block={block}
        onClick={onButtonClick}
        loading={loading && !showConfirmation}
        className={className}
      >
        {text}
      </Button>

      {showConfirmation && (
        <Modal
          title="Confirm Logout"
          open={isModalVisible}
          onOk={onLogoutConfirm}
          onCancel={handleCancel}
          okText="Logout"
          cancelText="Cancel"
          okButtonProps={{
            loading: loading,
            danger: true,
          }}
        >
          <p>Are you sure you want to logout?</p>
        </Modal>
      )}
    </>
  );
};

export default LogoutButton;
