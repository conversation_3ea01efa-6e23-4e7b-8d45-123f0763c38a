/**
 * CandidateTable Component for Interviewer Dashboard
 * 
 * Displays candidate information in a table format
 */

import React from 'react';
import { Card, Table, Avatar, Tag, Button } from 'antd';
import { UserOutlined, EyeOutlined } from '@ant-design/icons';

const CandidateTable = ({ data = [], title = "Candidates" }) => {
  const columns = [
    {
      title: 'Candidate',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div className="flex items-center">
          <Avatar 
            src={record.avatar} 
            icon={<UserOutlined />}
            className="mr-3"
          />
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-sm text-gray-500">{record.department}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      width: 80,
    },
    {
      title: 'Score',
      dataIndex: 'score',
      key: 'score',
      width: 100,
      render: (score) => (
        <Tag color={score >= 80 ? 'green' : score >= 60 ? 'orange' : 'red'}>
          {score}%
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => (
        <Tag color={status === 'Permanent' ? 'blue' : 'purple'}>
          {status}
        </Tag>
      ),
    },
    {
      title: 'Action',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button 
          type="link" 
          icon={<EyeOutlined />}
          size="small"
        >
          View
        </Button>
      ),
    },
  ];

  return (
    <Card title={title}>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        scroll={{ x: 800 }}
      />
    </Card>
  );
};

export default CandidateTable;
