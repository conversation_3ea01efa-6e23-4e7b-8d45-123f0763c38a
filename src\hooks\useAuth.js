import { useCallback } from 'react';
import useAuthStore from '@/store/auth.store';
import { supabase } from '@/utils/supabaseClient';

/**
 * Optimized useAuth Hook
 *
 * This hook handles all authentication services and provides a clean interface
 * to the auth store for state management.
 *
 * Architecture:
 * - Auth Store: Pure state management using Zustand (no service calls)
 * - This Hook: Handles all Supabase service calls and auth logic
 * - Role Stores: Handle role-specific business logic (candidate, company, interviewer)
 *
 * Responsibilities:
 * - Authentication services (login, logout, session management)
 * - Profile services (fetch, save, update)
 * - Auth listeners and session handling
 * - User role detection and management
 * - Interface to auth store for state management
 */
export const useAuth = () => {
  // All state from auth store (pure state management)
  const authStore = useAuthStore();
  const {
    user,
    role,
    rolePath,
    profile,
    profileCompletion,
    loading,
    error,
    // State setters
    setUser,
    setRole,
    setProfile,
    updateProfile: updateProfileState,
    setLoading,
    setError,
    clearError,
    // Cache management
    updateProfileCache,
    isProfileCacheValid,
    clearProfileCache,
    // Initialization
    setInitialized,
    isInitialized,
    // Auth listener
    setAuthListener,
    getAuthListener,
    // Reset
    resetAuth,
  } = authStore;

  // === AUTHENTICATION SERVICES ===
  // Handle authenticated user
  const handleAuthenticatedUser = useCallback(
    async (user) => {
      setUser(user);

      // Get role from metadata or database
      let userRole = user.user_metadata?.role;

      if (!userRole) {
        const { data: profileData } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        userRole = profileData?.role;
      }

      if (userRole) {
        setRole(userRole);

        // Fetch profile data after setting role
        try {
          const viewName = `${userRole}_profiles_complete`;

          // Try complete profile first
          const { data: completeProfile, error: viewError } = await supabase
            .from(viewName)
            .select('*')
            .eq('id', user.id)
            .single();

          let profileData = completeProfile;

          // Fallback to basic profile if view fails
          if (viewError && viewError.code === 'PGRST116') {
            const { data: basicProfile } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', user.id)
              .single();

            profileData = basicProfile || {
              id: user.id,
              email: user.email,
              role: userRole,
              created_at: new Date(),
            };
          }

          if (profileData) {
            setProfile(profileData);
            updateProfileCache();
          }
        } catch (error) {
          console.error('Error fetching profile during auth:', error);
          // Don't throw here, just log the error
        }
      } else {
        throw new Error('User role not configured');
      }

      setLoading(false);
    },
    [setUser, setRole, setProfile, updateProfileCache, setLoading]
  );

  // Set up auth listener
  const setupAuthListener = useCallback(async () => {
    const existingListener = getAuthListener();
    if (existingListener) return; // Already set up

    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      try {
        if (event === 'SIGNED_IN' && session?.user) {
          await handleAuthenticatedUser(session.user);
        } else if (event === 'SIGNED_OUT') {
          // Only reset if not already reset by logout function
          if (user || authStore.isAuthenticated) {
            resetAuth();
          }
        }
      } catch (error) {
        console.error('Auth state change error:', error);
        setError(error.message);
      }
    });

    setAuthListener(authListener);
  }, [
    getAuthListener,
    handleAuthenticatedUser,
    user,
    authStore.isAuthenticated,
    resetAuth,
    setError,
    setAuthListener,
  ]);

  // Initialize authentication
  const initializeAuth = useCallback(async () => {
    // Skip if already loading or initialized
    if (loading || isInitialized()) return;

    setLoading(true);
    clearError();

    try {
      // Get session with timeout
      const sessionPromise = supabase.auth.getSession();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Session timeout')), 8000)
      );

      const {
        data: { session },
        error: sessionError,
      } = await Promise.race([sessionPromise, timeoutPromise]);

      if (sessionError) throw sessionError;

      if (session?.user) {
        await handleAuthenticatedUser(session.user);
      } else {
        // No session found - user is not logged in
        // This is normal for first-time visitors or logged-out users
        setLoading(false);
      }

      // Set up auth listener
      await setupAuthListener();

      setInitialized(true);
      setLoading(false);
    } catch (error) {
      console.error('Auth initialization failed:', error);
      setError(error.message);
      setInitialized(true);
      setLoading(false);
    }
  }, [
    loading,
    isInitialized,
    setLoading,
    clearError,
    setInitialized,
    setError,
    handleAuthenticatedUser,
    setupAuthListener,
  ]);

  // Login function
  const login = useCallback(
    async (email, password) => {
      setLoading(true);
      clearError();

      try {
        const { data, error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) throw error;

        // User and role will be set by auth state change listener
        return { success: true, user: data.user };
      } catch (error) {
        setLoading(false);
        setError(error.message);
        return { success: false, message: error.message };
      }
    },
    [setLoading, clearError, setError]
  );

  // Logout function
  const logout = useCallback(async () => {
    // Set a timeout to ensure loading state is reset even if the operation hangs
    const safetyTimeout = setTimeout(() => {
      console.log('Logout safety timeout triggered');
      setLoading(false);
      resetAuth();
    }, 5000); // 5 second safety timeout

    setLoading(true);
    try {
      console.log('logout loading:', loading);
      const { error } = await supabase.auth.signOut();
      console.log('logout error:', error);
      if (error) throw error;

      // Don't wait for auth state change listener - reset immediately
      resetAuth();

      return { success: true };
    } catch (error) {
      console.error('Logout error caught:', error);
      setError(error.message);

      // Force reset even if logout fails
      resetAuth();
      return { success: false, message: error.message };
    } finally {
      clearTimeout(safetyTimeout);
      setLoading(false);
    }
  }, [setError, resetAuth, setLoading]);

  // === PROFILE SERVICES ===

  // Fetch profile
  const fetchProfile = useCallback(
    async (userId, forceRefresh = false, skipLoadingState = false) => {
      if (!userId || !role) return null;

      // Check cache validity unless force refresh
      if (!forceRefresh && profile && isProfileCacheValid()) {
        return profile;
      }

      if (!skipLoadingState) {
        setLoading(true);
        clearError();
      }

      try {
        const viewName = `${role}_profiles_complete`;

        // Try complete profile first
        const { data: completeProfile, error: viewError } = await supabase
          .from(viewName)
          .select('*')
          .eq('id', userId)
          .single();

        let profileData = completeProfile;

        // Fallback to basic profile if view fails
        if (viewError && viewError.code === 'PGRST116') {
          const { data: basicProfile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();

          profileData = basicProfile || {
            id: userId,
            email: user?.email,
            role: role,
            created_at: new Date(),
          };
        }

        setProfile(profileData);
        updateProfileCache();

        if (!skipLoadingState) {
          setLoading(false);
        }

        return profileData;
      } catch (error) {
        console.error('Error fetching profile:', error);
        if (!skipLoadingState) {
          setError(error.message);
          setLoading(false);
        }
        return null;
      }
    },
    [
      role,
      profile,
      isProfileCacheValid,
      setLoading,
      clearError,
      setProfile,
      updateProfileCache,
      setError,
      user?.email,
    ]
  );

  // Save profile
  const saveProfile = useCallback(
    async (userId, profileData) => {
      if (!userId || !role) return { success: false, error: 'Missing user ID or role' };

      setLoading(true);
      clearError();

      try {
        // Step 1: Update/insert profiles table (common fields)
        const { error: profileError } = await supabase.from('profiles').upsert({
          id: userId,
          email: profileData.email,
          phone_number: profileData.phone_number,
          role: role,
          username: profileData.username || profileData.full_name || profileData.company_name,
          updated_at: new Date(),
        });

        if (profileError) throw profileError;

        // Step 2: Update/insert role-specific data
        const roleTableMap = {
          candidate: 'candidate_profiles',
          company: 'company_profiles',
          interviewer: 'interviewer_profiles',
        };

        const roleTableName = roleTableMap[role];
        if (!roleTableName) throw new Error(`Unknown role: ${role}`);

        const roleData = { ...profileData };
        // Remove common fields from role-specific data
        delete roleData.email;
        delete roleData.phone_number;
        delete roleData.role;
        delete roleData.username;

        // Check if role-specific profile exists
        const { data: existingProfile } = await supabase
          .from(roleTableName)
          .select('id')
          .eq('id', userId)
          .single();

        const roleOperation = existingProfile
          ? supabase
              .from(roleTableName)
              .update({ ...roleData, updated_at: new Date() })
              .eq('id', userId)
          : supabase
              .from(roleTableName)
              .insert({ id: userId, ...roleData, created_at: new Date() });

        const { error: roleError } = await roleOperation;
        if (roleError) throw roleError;

        // Refresh profile data from database (without triggering loading state)
        const refreshedProfile = await fetchProfile(userId, true, true);

        setLoading(false);
        return { success: true, data: refreshedProfile };
      } catch (error) {
        console.error('Error saving profile:', error);
        setError(error.message);
        setLoading(false);
        return { success: false, error: error.message };
      }
    },
    [role, setLoading, clearError, setError, fetchProfile]
  );

  // Update profile (local state only)
  const updateProfile = useCallback(
    (updates) => {
      updateProfileState(updates);
    },
    [updateProfileState]
  );

  return {
    // Auth state
    user,
    role,
    isAuthenticated: !!user,

    // User profile state
    profile,
    profileCompletion,
    rolePath,

    // Combined UI state
    loading,
    error,

    // Initialization state
    isInitialized: isInitialized(),

    // Auth methods
    login,
    logout,
    initializeAuth,

    // Profile methods
    fetchProfile,
    saveProfile,
    updateProfile,

    // Store methods for advanced usage
    resetAuth,
  };
};

export default useAuth;
