/**
 * Interviewer Calendar Page
 *
 * Calendar page specifically designed for interviewers with:
 * - Assessment scheduling and deadlines
 * - Preparation time blocks
 * - Feedback sessions
 * - Training sessions
 * - Team meetings
 * - Google Calendar integration
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Space,
  Button,
  Alert,
  Row,
  Col,
  Statistic,
  Tag,
  List,
  Avatar,
} from 'antd';
import {
  CalendarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  TeamOutlined,
  BookOutlined,
  GoogleOutlined,
  PlusOutlined,
  UserOutlined,
  StarOutlined,
  BellOutlined,
} from '@ant-design/icons';
import Calendar from '@/components/shared/Calendar';
import useAuth from '@/hooks/useAuth';
import useInterviewerStore from '@/features/interviewer/store/interviewer.store';
import { INTERVIEWER_EVENT_TYPES } from '@/features/interviewer/constants';
import { getCalendarEvents, getInterviewerRequests } from '@/services/interviewRequest.service';

const { Title, Text } = Typography;

const InterviewerCalendar = () => {
  const { user, profile } = useAuth();
  const {
    assessmentRequests,
    completedAssessments,
    performanceMetrics,
    fetchAssessmentRequests,
    fetchCompletedAssessments,
    fetchPerformanceMetrics,
    loading,
  } = useInterviewerStore();

  const [calendarStats, setCalendarStats] = useState({
    upcomingAssessments: 0,
    completedThisWeek: 0,
    pendingFeedback: 0,
    totalEvents: 0,
  });

  const [interviewRequests, setInterviewRequests] = useState([]);
  const [calendarEvents, setCalendarEvents] = useState([]);

  // Load interviewer data on component mount
  useEffect(() => {
    if (profile?.id) {
      fetchAssessmentRequests(profile.id);
      fetchCompletedAssessments(profile.id);
      fetchPerformanceMetrics(profile.id);
      loadInterviewRequests();
      loadCalendarEvents();
    }
  }, [profile?.id, fetchAssessmentRequests, fetchCompletedAssessments, fetchPerformanceMetrics]);

  // Load interview requests
  const loadInterviewRequests = async () => {
    if (!user?.id) return;

    try {
      const { success, data } = await getInterviewerRequests(user.id);
      if (success) {
        setInterviewRequests(data);
      }
    } catch (error) {
      console.error('Error loading interview requests:', error);
    }
  };

  // Load calendar events
  const loadCalendarEvents = async () => {
    if (!user?.id) return;

    try {
      const { success, data } = await getCalendarEvents(user.id, 'interviewer');
      if (success) {
        setCalendarEvents(data);
      }
    } catch (error) {
      console.error('Error loading calendar events:', error);
    }
  };

  // Calculate calendar statistics
  useEffect(() => {
    const now = new Date();
    const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);

    const upcomingAssessments =
      assessmentRequests?.filter(
        (req) => req.status === 'pending' && new Date(req.deadline) > new Date()
      ).length || 0;

    const completedThisWeek =
      completedAssessments?.filter((assessment) => {
        const completedDate = new Date(assessment.completed_at);
        return completedDate >= weekStart && completedDate <= weekEnd;
      }).length || 0;

    const pendingFeedback =
      assessmentRequests?.filter((req) => req.status === 'completed' && !req.feedback_provided)
        .length || 0;

    setCalendarStats({
      upcomingAssessments,
      completedThisWeek,
      pendingFeedback,
      totalEvents: upcomingAssessments + completedThisWeek + pendingFeedback,
    });
  }, [assessmentRequests, completedAssessments]);

  // Get participants for calendar events
  const getParticipants = () => {
    const participants = [];

    // Add candidates from assessment requests
    assessmentRequests?.forEach((request) => {
      if (request.candidate) {
        participants.push({
          id: request.candidate.id,
          name: request.candidate.full_name,
          email: request.candidate.email,
          role: 'candidate',
        });
      }
    });

    return participants;
  };

  // Interviewer-specific event types
  const interviewerEventTypes = INTERVIEWER_EVENT_TYPES;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <Text>Loading calendar...</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="interviewer-calendar-page p-6">
      {/* Page Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CalendarOutlined className="text-2xl text-primary mr-3" />
            <div>
              <Title
                level={2}
                className="m-0"
              >
                Calendar
              </Title>
              <Text type="secondary">
                Manage your assessment schedule and sync with Google Calendar
              </Text>
            </div>
          </div>
          <Button
            type="primary"
            icon={<GoogleOutlined />}
            size="large"
          >
            Sync Google Calendar
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <Row
        gutter={16}
        className="mb-6"
      >
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Upcoming Assessments"
              value={calendarStats.upcomingAssessments}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Completed This Week"
              value={calendarStats.completedThisWeek}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Pending Feedback"
              value={calendarStats.pendingFeedback}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card>
            <Statistic
              title="Total Events"
              value={calendarStats.totalEvents}
              prefix={<CalendarOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Card
        className="mb-6"
        size="small"
      >
        <div className="flex items-center justify-between">
          <Text strong>Quick Actions</Text>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="small"
            >
              Block Time
            </Button>
            <Button
              icon={<FileTextOutlined />}
              size="small"
            >
              Schedule Feedback
            </Button>
            <Button
              icon={<BookOutlined />}
              size="small"
            >
              Training Session
            </Button>
            <Button
              icon={<TeamOutlined />}
              size="small"
            >
              Team Meeting
            </Button>
            <Button
              icon={<BellOutlined />}
              size="small"
            >
              Set Reminder
            </Button>
          </Space>
        </div>
      </Card>

      {/* Main Calendar Layout */}
      <Row gutter={16}>
        {/* Sidebar */}
        <Col
          xs={24}
          lg={6}
        >
          {/* My Schedule */}
          <Card
            title="My Schedule"
            className="mb-4"
            size="small"
          >
            <Space
              direction="vertical"
              style={{ width: '100%' }}
            >
              <div className="flex items-center justify-between">
                <Text>Assessment Reviews</Text>
                <Tag color="blue">{calendarStats.upcomingAssessments}</Tag>
              </div>
              <div className="flex items-center justify-between">
                <Text>Feedback Sessions</Text>
                <Tag color="green">{calendarStats.pendingFeedback}</Tag>
              </div>
              <div className="flex items-center justify-between">
                <Text>Training</Text>
                <Tag color="purple">2</Tag>
              </div>
              <div className="flex items-center justify-between">
                <Text>Team Meetings</Text>
                <Tag color="cyan">1</Tag>
              </div>
              <div className="flex items-center justify-between">
                <Text>Breaks</Text>
                <Tag color="gray">3</Tag>
              </div>
            </Space>
          </Card>

          {/* Categories */}
          <Card
            title="Categories"
            className="mb-4"
            size="small"
          >
            <Space
              direction="vertical"
              style={{ width: '100%' }}
            >
              {interviewerEventTypes.map((type) => (
                <div
                  key={type.id}
                  className="flex items-center"
                >
                  <div
                    className="w-3 h-3 rounded-full mr-2"
                    style={{
                      backgroundColor:
                        type.color === 'blue'
                          ? '#1890ff'
                          : type.color === 'green'
                            ? '#52c41a'
                            : type.color === 'orange'
                              ? '#faad14'
                              : type.color === 'purple'
                                ? '#722ed1'
                                : type.color === 'cyan'
                                  ? '#13c2c2'
                                  : '#8c8c8c',
                    }}
                  />
                  <Text>{type.name}</Text>
                </div>
              ))}
            </Space>
          </Card>

          {/* Recent Activity */}
          <Card
            title="Recent Activity"
            size="small"
          >
            <List
              size="small"
              dataSource={[
                {
                  title: 'Assessment completed',
                  description: 'John Doe - Frontend Developer',
                  time: '2 hours ago',
                },
                {
                  title: 'Feedback submitted',
                  description: 'Sarah Wilson - Product Manager',
                  time: '4 hours ago',
                },
                {
                  title: 'Training session',
                  description: 'Advanced Assessment Techniques',
                  time: '1 day ago',
                },
              ]}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={<UserOutlined />}
                        size="small"
                      />
                    }
                    title={
                      <Text
                        strong
                        style={{ fontSize: '12px' }}
                      >
                        {item.title}
                      </Text>
                    }
                    description={
                      <div>
                        <Text style={{ fontSize: '11px' }}>{item.description}</Text>
                        <br />
                        <Text
                          type="secondary"
                          style={{ fontSize: '10px' }}
                        >
                          {item.time}
                        </Text>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Main Calendar */}
        <Col
          xs={24}
          lg={18}
        >
          <Calendar
            userType="interviewer"
            eventTypes={interviewerEventTypes}
            participants={getParticipants()}
            viewOptions={{
              month: true,
              day: true,
              agenda: true,
            }}
          />
        </Col>
      </Row>
    </div>
  );
};

export default InterviewerCalendar;
