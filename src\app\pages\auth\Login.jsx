import { useState, useEffect } from 'react';
import { useNavi<PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button, Card, Form, Input, Typography, Divider, Checkbox, Alert } from 'antd';
import { MailOutlined, LockOutlined, LoginOutlined, LinkedinOutlined } from '@ant-design/icons';
import { facebook_icon, google_icon } from '@/assets';
import { supabase } from '@/utils/supabaseClient';
import useAuth from '@/hooks/useAuth';
import showToast from '@/utils/toast';

const { Title, Text, Paragraph } = Typography;

const Login = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { user, role, rolePath, login, loading } = useAuth();
  const [error, setError] = useState(null);
  const [rememberMe, setRememberMe] = useState(true);

  // Redirect if already logged in
  useEffect(() => {
    if (user && (role || rolePath)) {
      const path =
        rolePath || (role === 'interviewer' ? 'sourcer' : role === 'company' ? 'org' : role);
      if (path) {
        navigate(`/${path}/dashboard`, { replace: true });
      }
    }
  }, [user, role, rolePath, navigate]);

  const handleLogin = async (values) => {
    setError(null);

    try {
      // Call the login function with email and password
      const { success, message: errorMessage } = await login(values.email, values.password);

      if (!success) {
        throw new Error(errorMessage);
      }

      showToast.success('Login successful!');
      // Don't navigate here - let the useEffect handle navigation after auth state updates
    } catch (error) {
      let errorMessage = error.message;

      // Provide better error messages for common issues
      if (
        error.message.includes('Email not confirmed') ||
        error.message.includes('email_not_confirmed')
      ) {
        errorMessage =
          'Please verify your email address before logging in. Check your email for the verification code or try registering again to resend it.';
      } else if (error.message.includes('Invalid login credentials')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      }

      setError(errorMessage);
      showToast.error('Login failed: ' + errorMessage);
    }
  };

  const handleSocialLogin = async (provider) => {
    setError(null);
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/login`,
        },
      });

      if (error) throw error;
    } catch (error) {
      setError(error.message);
      showToast.error('Social login failed: ' + error.message);
    }
  };

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <div className="flex justify-center">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={fadeIn}
        className="w-full max-w-md"
      >
        <Card
          variant="bordered"
          className="shadow-lg rounded-xl overflow-hidden"
        >
          <div className="p-1 sm:p-2 md:p-6">
            <div className="text-center mb-6">
              <Title
                level={2}
                className="mb-2"
              >
                Welcome Back
              </Title>
              <Paragraph className="text-text-secondary">
                Sign in to your account to continue
              </Paragraph>
            </div>

            {error && (
              <Alert
                message="Login Failed"
                description={error}
                type="error"
                showIcon
                className="mb-6"
                closable
                onClose={() => setError(null)}
              />
            )}

            <Form
              form={form}
              layout="vertical"
              onFinish={handleLogin}
              requiredMark={false}
              initialValues={{ remember: rememberMe }}
            >
              <Form.Item
                name="email"
                rules={[
                  { required: true, message: 'Please enter your email' },
                  { type: 'email', message: 'Please enter a valid email' },
                ]}
              >
                <Input
                  prefix={<MailOutlined className="text-text-secondary" />}
                  placeholder="Email address"
                  size="large"
                  className="rounded-lg h-12"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[{ required: true, message: 'Please enter your password' }]}
              >
                <Input.Password
                  prefix={<LockOutlined className="text-text-secondary" />}
                  placeholder="Password"
                  size="large"
                  className="rounded-lg h-12"
                />
              </Form.Item>

              <div className="flex justify-between items-center mb-4">
                <Form.Item
                  name="remember"
                  valuePropName="checked"
                  noStyle
                >
                  <Checkbox
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  >
                    Remember me
                  </Checkbox>
                </Form.Item>

                <Link
                  to="/forgot-password"
                  className="text-primary hover:text-primary-hover"
                >
                  Forgot password?
                </Link>
              </div>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  block
                  loading={loading}
                  icon={<LoginOutlined />}
                  className="h-12 rounded-lg font-medium"
                >
                  Sign In
                </Button>
              </Form.Item>
            </Form>

            <Divider plain>Or continue with</Divider>

            <div className="grid grid-cols-3 gap-3 mb-6">
              <Button
                onClick={() => handleSocialLogin('google')}
                loading={loading}
                disabled={loading}
                className="flex items-center justify-center gap-2"
              >
                <img
                  src={google_icon}
                  alt="Google Icon"
                  className="w-5 h-5"
                />
                Google
              </Button>
              <Button
                onClick={() => handleSocialLogin('facebook')}
                loading={loading}
                disabled={loading}
                className="flex items-center justify-center gap-2"
              >
                <img
                  src={facebook_icon}
                  alt="Facebook Icon"
                  className="w-5 h-5"
                />
                Facebook
              </Button>
              <Button
                onClick={() => handleSocialLogin('linkedin')}
                loading={loading}
                disabled={loading}
                icon={<LinkedinOutlined />}
                className="flex items-center justify-center gap-2"
              >
                LinkedIn
              </Button>
            </div>

            <div className="text-center">
              <Text className="text-text-secondary">
                Don't have an account?{' '}
                <Link
                  to="/register"
                  className="text-primary hover:text-primary-hover font-medium"
                >
                  Sign up
                </Link>
              </Text>
            </div>
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default Login;
