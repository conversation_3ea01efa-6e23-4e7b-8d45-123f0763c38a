/**
 * useInterviewer Hook
 * 
 * Custom hook for interviewer-specific operations
 * Integrates with interviewer store and services
 */

import { useEffect } from 'react';
import useInterviewerStore from '../store/interviewer.store';
import {
  getAssessmentRequests,
  getCompletedAssessments,
  submitCandidateEvaluation,
  getInterviewerEarnings,
  getPerformanceMetrics
} from '../services/interviewer.service';
import useAuth from '@/hooks/useAuth';
import showToast from '@/utils/toast';

const useInterviewer = () => {
  const { user, profile } = useAuth();
  
  const {
    // State
    assessmentRequests,
    completedAssessments,
    earnings,
    performanceMetrics,
    loading,
    error,
    assessmentsLoading,
    evaluationsLoading,
    earningsLoading,
    metricsLoading,
    
    // Actions
    setLoading,
    setError,
    clearError,
    fetchAssessmentRequests,
    fetchCompletedAssessments,
    fetchEarnings,
    fetchPerformanceMetrics,
    submitAssessmentEvaluation,
    resetStore
  } = useInterviewerStore();

  // Auto-load data when user changes
  useEffect(() => {
    if (user && profile?.role === 'interviewer') {
      loadInterviewerData();
    }
  }, [user, profile]);

  /**
   * Load all interviewer data
   */
  const loadInterviewerData = async () => {
    if (!user) return;

    try {
      await Promise.all([
        loadAssessmentRequests(),
        loadCompletedAssessments(),
        loadEarnings(),
        loadPerformanceMetrics()
      ]);
    } catch (error) {
      console.error('Error loading interviewer data:', error);
      showToast.error('Failed to load interviewer data');
    }
  };

  /**
   * Load assessment requests
   */
  const loadAssessmentRequests = async (forceRefresh = false) => {
    if (!user) return;

    try {
      await fetchAssessmentRequests(user.id, forceRefresh);
    } catch (error) {
      console.error('Error loading assessment requests:', error);
      showToast.error('Failed to load assessment requests');
    }
  };

  /**
   * Load completed assessments
   */
  const loadCompletedAssessments = async (forceRefresh = false) => {
    if (!user) return;

    try {
      await fetchCompletedAssessments(user.id, forceRefresh);
    } catch (error) {
      console.error('Error loading completed assessments:', error);
      showToast.error('Failed to load completed assessments');
    }
  };

  /**
   * Load earnings data
   */
  const loadEarnings = async () => {
    if (!user) return;

    try {
      await fetchEarnings(user.id);
    } catch (error) {
      console.error('Error loading earnings:', error);
      showToast.error('Failed to load earnings data');
    }
  };

  /**
   * Load performance metrics
   */
  const loadPerformanceMetrics = async () => {
    if (!user) return;

    try {
      await fetchPerformanceMetrics(user.id);
    } catch (error) {
      console.error('Error loading performance metrics:', error);
      showToast.error('Failed to load performance metrics');
    }
  };

  /**
   * Submit evaluation for an assessment
   */
  const submitEvaluation = async (assessmentId, evaluationData) => {
    try {
      const result = await submitAssessmentEvaluation(assessmentId, evaluationData);
      
      if (result.success) {
        showToast.success('Evaluation submitted successfully');
        // Refresh data
        await Promise.all([
          loadAssessmentRequests(true),
          loadCompletedAssessments(true),
          loadPerformanceMetrics()
        ]);
        return result;
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error submitting evaluation:', error);
      showToast.error('Failed to submit evaluation');
      return { success: false, error: error.message };
    }
  };

  /**
   * Refresh all data
   */
  const refreshData = async () => {
    await loadInterviewerData();
  };

  /**
   * Get dashboard statistics
   */
  const getDashboardStats = () => {
    return {
      pendingAssessments: assessmentRequests?.length || 0,
      completedAssessments: completedAssessments?.length || 0,
      totalEarnings: earnings?.total || 0,
      thisMonthEarnings: earnings?.thisMonth || 0,
      averageScore: performanceMetrics?.averageScores?.overall || 0,
      completionRate: performanceMetrics?.completionRate || 0
    };
  };

  /**
   * Get recent activities
   */
  const getRecentActivities = () => {
    const activities = [];

    // Add recent completed assessments
    if (completedAssessments) {
      completedAssessments.slice(0, 5).forEach(assessment => {
        activities.push({
          id: `assessment-${assessment.id}`,
          type: 'assessment_completed',
          title: 'Assessment Completed',
          description: `Evaluated ${assessment.candidates?.full_name} for ${assessment.jobs?.title}`,
          timestamp: assessment.updated_at,
          data: assessment
        });
      });
    }

    // Sort by timestamp
    return activities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  };

  return {
    // State
    assessmentRequests,
    completedAssessments,
    earnings,
    performanceMetrics,
    loading,
    error,
    assessmentsLoading,
    evaluationsLoading,
    earningsLoading,
    metricsLoading,

    // Actions
    loadInterviewerData,
    loadAssessmentRequests,
    loadCompletedAssessments,
    loadEarnings,
    loadPerformanceMetrics,
    submitEvaluation,
    refreshData,
    clearError,
    resetStore,

    // Computed
    getDashboardStats,
    getRecentActivities,

    // Utils
    isInterviewer: profile?.role === 'interviewer'
  };
};

export default useInterviewer;
