import React, { useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Button,
  List,
  Avatar,
  Tag,
  Space,
  Alert,
  Badge,
  Divider,
  Progress,
} from 'antd';
import {
  UserOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  TrophyOutlined,
  CalendarOutlined,
  FileTextOutlined,
  MessageOutlined,
  BellOutlined,
  RiseOutlined,
  StarOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import useInterviewer from '../hooks/useInterviewer';
import useAuth from '@/hooks/useAuth';
import InterviewRequests from './InterviewRequests';

const { Title, Text } = Typography;

const InterviewerDashboard = () => {
  const { user, profile } = useAuth();
  const {
    assessmentRequests,
    completedAssessments,
    earnings,
    performanceMetrics,
    loading,
    error,
    getDashboardStats,
    getRecentActivities,
    loadInterviewerData,
  } = useInterviewer();

  const stats = getDashboardStats();
  const recentActivities = getRecentActivities();

  useEffect(() => {
    if (user && profile?.role === 'interviewer') {
      loadInterviewerData();
    }
  }, [user, profile]);

  if (!user || profile?.role !== 'interviewer') {
    return (
      <Alert
        message="Access Denied"
        description="This page is only accessible to interviewers."
        type="error"
        showIcon
      />
    );
  }

  return (
    <div className="interviewer-dashboard p-6">
      {/* Welcome Section */}
      <div className="mb-6">
        <Row
          align="middle"
          justify="space-between"
        >
          <Col>
            <div className="flex items-center">
              <Avatar
                size={64}
                src={profile?.profile_photo_url}
                icon={<UserOutlined />}
                className="mr-4"
              />
              <div>
                <Title
                  level={2}
                  className="mb-1"
                >
                  {profile?.full_name || 'Interviewer'}
                </Title>
                <Text
                  type="secondary"
                  className="text-base"
                >
                  Welcome back! Here's your interview overview
                </Text>
                <div className="flex items-center mt-2">
                  <StarOutlined className="text-yellow-500 mr-1" />
                  <Text strong>4.8 Rating</Text>
                  <Divider type="vertical" />
                  <TrophyOutlined className="text-blue-500 mr-1" />
                  <Text strong>Top Performer</Text>
                </div>
              </div>
            </div>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<CalendarOutlined />}
                size="large"
              >
                View Calendar
              </Button>
              <Button
                icon={<BellOutlined />}
                size="large"
              >
                <Badge
                  count={5}
                  size="small"
                >
                  Notifications
                </Badge>
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          closable
          className="mb-6"
        />
      )}

      {/* Enhanced Statistics Cards */}
      <Row
        gutter={16}
        className="mb-6"
      >
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {stats.pendingAssessments || 16}
            </div>
            <div className="text-gray-600 mb-1">Pending Interviews</div>
            <div className="text-sm text-gray-400">Applicants</div>
            <div className="mt-2">
              <Progress
                percent={75}
                showInfo={false}
                strokeColor="#1890ff"
                size="small"
              />
            </div>
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {stats.completedAssessments || 25}
            </div>
            <div className="text-gray-600 mb-1">Completed Interviews</div>
            <div className="text-sm text-gray-400">Applicants</div>
            <div className="mt-2">
              <Progress
                percent={85}
                showInfo={false}
                strokeColor="#52c41a"
                size="small"
              />
            </div>
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              ₹{stats.totalEarnings || '4,30,000'}/-
            </div>
            <div className="text-gray-600 mb-1">Total Earning</div>
            <div className="text-sm text-gray-400">Rupees</div>
            <div className="mt-2 flex items-center justify-center">
              <RiseOutlined className="text-green-500 mr-1" />
              <Text className="text-green-500 text-sm">+12% this month</Text>
            </div>
          </Card>
        </Col>
        <Col
          xs={24}
          sm={12}
          md={6}
        >
          <Card className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">
              {stats.pendingAssessments + stats.completedAssessments || 41}
            </div>
            <div className="text-gray-600 mb-1">Total Interviews</div>
            <div className="text-sm text-gray-400">Applicants</div>
            <div className="mt-2">
              <Progress
                percent={90}
                showInfo={false}
                strokeColor="#faad14"
                size="small"
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* Interview Requests Section */}
      <InterviewRequests />

      <Row gutter={16}>
        {/* Pending Assessments */}
        <Col
          xs={24}
          lg={12}
        >
          <Card
            title="Pending Assessments"
            extra={
              <Button
                type="link"
                size="small"
              >
                View All
              </Button>
            }
            className="mb-6"
          >
            {assessmentRequests && assessmentRequests.length > 0 ? (
              <List
                itemLayout="horizontal"
                dataSource={assessmentRequests.slice(0, 5)}
                renderItem={(assessment) => (
                  <List.Item
                    actions={[
                      <Button
                        type="primary"
                        size="small"
                      >
                        Start Assessment
                      </Button>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          src={assessment.candidates?.profile_photo_url}
                          icon={<UserOutlined />}
                        />
                      }
                      title={assessment.candidates?.full_name}
                      description={
                        <div>
                          <div>{assessment.jobs?.title}</div>
                          <div className="text-xs text-gray-500">
                            {assessment.jobs?.companies?.company_name}
                          </div>
                          <div className="mt-1">
                            <Tag color="orange">Pending</Tag>
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div className="text-center py-8">
                <Text type="secondary">No pending assessments</Text>
              </div>
            )}
          </Card>
        </Col>

        {/* Recent Activities */}
        <Col
          xs={24}
          lg={12}
        >
          <Card
            title="Recent Activities"
            extra={
              <Button
                type="link"
                size="small"
              >
                View All
              </Button>
            }
            className="mb-6"
          >
            {recentActivities && recentActivities.length > 0 ? (
              <List
                itemLayout="horizontal"
                dataSource={recentActivities.slice(0, 5)}
                renderItem={(activity) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          icon={
                            activity.type === 'assessment_completed' ? (
                              <CheckCircleOutlined />
                            ) : (
                              <FileTextOutlined />
                            )
                          }
                          style={{
                            backgroundColor:
                              activity.type === 'assessment_completed' ? '#52c41a' : '#1890ff',
                          }}
                        />
                      }
                      title={activity.title}
                      description={
                        <div>
                          <div className="text-sm">{activity.description}</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {new Date(activity.timestamp).toLocaleDateString()}
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div className="text-center py-8">
                <Text type="secondary">No recent activities</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* Performance Overview */}
      <Row gutter={16}>
        <Col
          xs={24}
          lg={16}
        >
          <Card
            title="Performance Overview"
            className="mb-6"
          >
            {performanceMetrics ? (
              <Row gutter={16}>
                <Col span={8}>
                  <Statistic
                    title="Completion Rate"
                    value={performanceMetrics.completionRate || 0}
                    precision={1}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="Average Technical Score"
                    value={performanceMetrics.averageScores?.technical || 0}
                    precision={1}
                    suffix="/ 10"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="Average Communication Score"
                    value={performanceMetrics.averageScores?.communication || 0}
                    precision={1}
                    suffix="/ 10"
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
              </Row>
            ) : (
              <div className="text-center py-8">
                <Text type="secondary">
                  Performance data will appear after completing assessments
                </Text>
              </div>
            )}
          </Card>
        </Col>

        <Col
          xs={24}
          lg={8}
        >
          <Card
            title="Quick Actions"
            className="mb-6"
          >
            <Space
              direction="vertical"
              style={{ width: '100%' }}
            >
              <Button
                type="primary"
                icon={<FileTextOutlined />}
                block
                disabled={!assessmentRequests || assessmentRequests.length === 0}
              >
                Start Next Assessment
              </Button>
              <Button
                icon={<CalendarOutlined />}
                block
              >
                View Calendar
              </Button>
              <Button
                icon={<DollarOutlined />}
                block
              >
                View Earnings
              </Button>
              <Button
                icon={<UserOutlined />}
                block
              >
                Update Profile
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default InterviewerDashboard;
