/**
 * Interview Requests Component
 * 
 * Displays pending interview requests for interviewers
 * Allows accepting/declining requests
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Avatar,
  Button,
  Space,
  Typography,
  Tag,
  Modal,
  Form,
  Input,
  message,
  Empty,
  Skeleton,
  Row,
  Col,
  Divider,
  Badge,
} from 'antd';
import {
  UserOutlined,
  CheckOutlined,
  CloseOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  BriefcaseOutlined,
  TeamOutlined,
  MessageOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { 
  getInterviewerRequests, 
  acceptInterviewRequest, 
  declineInterviewRequest 
} from '@/services/interviewRequest.service';
import useAuth from '@/hooks/useAuth';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const InterviewRequests = () => {
  const { user } = useAuth();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(null);
  const [declineModalVisible, setDeclineModalVisible] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [form] = Form.useForm();

  // Load interview requests
  useEffect(() => {
    if (user?.id) {
      loadRequests();
    }
  }, [user?.id]);

  const loadRequests = async () => {
    try {
      setLoading(true);
      const { success, data, error } = await getInterviewerRequests(user.id);
      
      if (success) {
        setRequests(data);
      } else {
        message.error(error || 'Failed to load interview requests');
      }
    } catch (error) {
      console.error('Error loading requests:', error);
      message.error('Failed to load interview requests');
    } finally {
      setLoading(false);
    }
  };

  const handleAccept = async (requestId) => {
    try {
      setActionLoading(requestId);
      const { success, error } = await acceptInterviewRequest(requestId);
      
      if (success) {
        message.success('Interview request accepted successfully!');
        loadRequests(); // Refresh the list
      } else {
        message.error(error || 'Failed to accept request');
      }
    } catch (error) {
      console.error('Error accepting request:', error);
      message.error('Failed to accept request');
    } finally {
      setActionLoading(null);
    }
  };

  const handleDecline = (request) => {
    setSelectedRequest(request);
    setDeclineModalVisible(true);
  };

  const handleDeclineSubmit = async (values) => {
    try {
      setActionLoading(selectedRequest.id);
      const { success, error } = await declineInterviewRequest(
        selectedRequest.id, 
        values.reason
      );
      
      if (success) {
        message.success('Interview request declined');
        setDeclineModalVisible(false);
        form.resetFields();
        loadRequests(); // Refresh the list
      } else {
        message.error(error || 'Failed to decline request');
      }
    } catch (error) {
      console.error('Error declining request:', error);
      message.error('Failed to decline request');
    } finally {
      setActionLoading(null);
    }
  };

  const formatDate = (dateString) => {
    return dayjs(dateString).format('MMM DD, YYYY [at] h:mm A');
  };

  const getExperienceColor = (years) => {
    if (years < 2) return 'green';
    if (years < 5) return 'blue';
    if (years < 10) return 'orange';
    return 'purple';
  };

  if (loading) {
    return (
      <Card title="Interview Requests" className="mb-6">
        <Skeleton active paragraph={{ rows: 3 }} />
      </Card>
    );
  }

  return (
    <>
      <Card 
        title={
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <MessageOutlined className="mr-2" />
              Interview Requests
              {requests.length > 0 && (
                <Badge count={requests.length} className="ml-2" />
              )}
            </div>
            <Button 
              type="text" 
              size="small" 
              onClick={loadRequests}
              loading={loading}
            >
              Refresh
            </Button>
          </div>
        }
        className="mb-6"
      >
        {requests.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No pending interview requests"
          />
        ) : (
          <List
            dataSource={requests}
            renderItem={(request) => (
              <List.Item
                key={request.id}
                className="border-b border-gray-100 last:border-b-0 py-4"
              >
                <div className="w-full">
                  <Row gutter={16} align="middle">
                    {/* Candidate Info */}
                    <Col xs={24} md={12}>
                      <div className="flex items-start space-x-3">
                        <Avatar
                          size={48}
                          src={request.candidate_photo}
                          icon={<UserOutlined />}
                        />
                        <div className="flex-1">
                          <Title level={5} className="mb-1">
                            {request.candidate_name}
                          </Title>
                          <Text type="secondary" className="block">
                            {request.candidate_email}
                          </Text>
                          <div className="flex items-center mt-1">
                            <BriefcaseOutlined className="mr-1 text-gray-400" />
                            <Text className="text-sm">
                              {request.current_job_title}
                            </Text>
                            <Divider type="vertical" />
                            <Tag color={getExperienceColor(request.years_experience)}>
                              {request.years_experience}+ years
                            </Tag>
                          </div>
                        </div>
                      </div>
                    </Col>

                    {/* Job Info */}
                    <Col xs={24} md={8}>
                      <div className="mt-3 md:mt-0">
                        <div className="flex items-center mb-2">
                          <TeamOutlined className="mr-1 text-gray-400" />
                          <Text strong>{request.job_title}</Text>
                        </div>
                        <Text type="secondary" className="block text-sm">
                          {request.company_name}
                        </Text>
                        <div className="flex items-center mt-1">
                          <CalendarOutlined className="mr-1 text-gray-400" />
                          <Text className="text-sm">
                            {formatDate(request.preferred_date)}
                          </Text>
                        </div>
                        <div className="flex items-center mt-1">
                          <ClockCircleOutlined className="mr-1 text-gray-400" />
                          <Text className="text-sm">
                            {request.duration_minutes || 60} minutes
                          </Text>
                        </div>
                      </div>
                    </Col>

                    {/* Actions */}
                    <Col xs={24} md={4}>
                      <div className="flex flex-col space-y-2 mt-3 md:mt-0">
                        <Button
                          type="primary"
                          icon={<CheckOutlined />}
                          size="small"
                          loading={actionLoading === request.id}
                          onClick={() => handleAccept(request.id)}
                          className="w-full"
                        >
                          Accept
                        </Button>
                        <Button
                          danger
                          icon={<CloseOutlined />}
                          size="small"
                          onClick={() => handleDecline(request)}
                          className="w-full"
                        >
                          Decline
                        </Button>
                      </div>
                    </Col>
                  </Row>

                  {/* Message */}
                  {request.message && (
                    <div className="mt-3 p-3 bg-gray-50 rounded">
                      <Text className="text-sm">
                        <strong>Message:</strong> {request.message}
                      </Text>
                    </div>
                  )}
                </div>
              </List.Item>
            )}
          />
        )}
      </Card>

      {/* Decline Modal */}
      <Modal
        title="Decline Interview Request"
        open={declineModalVisible}
        onCancel={() => {
          setDeclineModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleDeclineSubmit}
        >
          <Form.Item
            name="reason"
            label="Reason for declining (optional)"
          >
            <TextArea
              rows={4}
              placeholder="Please provide a reason for declining this interview request..."
            />
          </Form.Item>
          <Form.Item className="mb-0">
            <Space className="w-full justify-end">
              <Button onClick={() => setDeclineModalVisible(false)}>
                Cancel
              </Button>
              <Button 
                type="primary" 
                danger 
                htmlType="submit"
                loading={actionLoading === selectedRequest?.id}
              >
                Decline Request
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default InterviewRequests;
